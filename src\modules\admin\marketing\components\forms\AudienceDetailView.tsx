import React, { useState, useCallback, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, IconCard, Loading, Icon, Avatar, CollapsibleCard, Input, GenericCustomFieldSelector, PhoneInputWithCountry } from '@/shared/components/common';
import AdminTagsSelectWithPagination from '@/modules/admin/marketing/components/common/AdminTagsSelectWithPagination';
import CustomFieldRenderer from '@/modules/business/components/CustomFieldRenderer';
import { useAudience, useBulkUpdateAudienceCustomFields, useUpdateAudience } from '../../hooks/useAudienceQuery';
import { MarketingCustomFieldBusinessService } from '../../services/marketing-custom-field.service';
import { MarketingCustomFieldResponse } from '../../types/custom-field.types';
import { BulkUpdateCustomFieldsDto, CreateCustomFieldDto, UpdateAudienceRequest } from '../../types/audience.types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { createSafeMarketingCustomFieldParams } from '../../utils/api-params.utils';

// Interface cho trường tùy chỉnh đã chọn với giá trị - mở rộng từ MarketingCustomFieldResponse
interface SelectedCustomField extends MarketingCustomFieldResponse {
  value: Record<string, unknown>; // Giá trị người dùng nhập
}

interface AudienceDetailViewProps {
  /**
   * ID audience
   */
  audienceId: number;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Callback khi chỉnh sửa
   */
  onEdit?: () => void;
}

/**
 * Component hiển thị chi tiết audience
 */
const AudienceDetailView: React.FC<AudienceDetailViewProps> = ({ audienceId, onClose, onEdit }) => {
  const { t } = useTranslation(['marketing']);
  const smartNotification = useSmartNotification();

  // Sử dụng hook useAudience để lấy dữ liệu audience
  const { data: audience, isLoading, error } = useAudience(audienceId);

  // Hook để bulk update custom fields
  const bulkUpdateCustomFields = useBulkUpdateAudienceCustomFields(audienceId);

  // Hook để update audience
  const updateAudience = useUpdateAudience(audienceId);

  // State cho custom fields
  const [audienceCustomFields, setAudienceCustomFields] = useState<SelectedCustomField[]>([]);

  // Note: We don't need to fetch all custom fields here since we use searchFunction for dynamic loading

  // Create search function for GenericCustomFieldSelector - GỌI API THỰC SỰ
  const searchFunction = useCallback(async (params: { search?: string; page?: number; limit?: number }) => {
    try {
      // Use utility function to create safe parameters
      const apiParams = createSafeMarketingCustomFieldParams({
        search: params.search,
        page: params.page,
        limit: params.limit,
        sortBy: 'displayName',
        sortDirection: 'ASC',
      });

      console.log('📡 Calling MarketingCustomFieldBusinessService with:', apiParams);
      const response = await MarketingCustomFieldBusinessService.getCustomFieldsWithBusinessLogic(apiParams);
      console.log('✅ API response:', response);

      // Transform data for GenericCustomFieldSelector
      // API response structure: { result: { data: [...], meta: {...} } }
      const responseData = response.result as unknown as { data: MarketingCustomFieldResponse[]; meta: Record<string, unknown> };
      const items = responseData?.data || [];
      const meta = responseData?.meta || {};

      const transformedResult = {
        items: items.map((item: MarketingCustomFieldResponse, index: number) => ({
          id: index + 1, // Tạo ID tạm thời vì API không trả về id
          fieldKey: item.fieldKey, // Thêm fieldKey để có thể identify
          label: item.displayName,
          dataType: item.dataType,
          type: item.dataType,
          description: item.description,
          config: item.config,
        })),
        totalItems: meta['total'] as number || 0,
        totalPages: meta['totalPages'] as number || 0,
        currentPage: meta['page'] as number || 1,
        hasNextPage: (meta['page'] as number || 1) < (meta['totalPages'] as number || 1),
      };

      console.log('🔄 Transformed result:', transformedResult);
      return transformedResult;
    } catch (error) {
      console.error('❌ Error in AudienceDetailView search:', error);
      throw error;
    }
  }, []);

  // State cho avatar
  const [avatarUrl, setAvatarUrl] = useState<string>('');
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [pendingAvatarFile, setPendingAvatarFile] = useState<File | null>(null); // File đang chờ upload
  const [pendingAvatarUrl, setPendingAvatarUrl] = useState<string>(''); // Preview URL
  const fileInputRef = useRef<HTMLInputElement>(null);

  // State cho editing mode
  const [isEditingGeneral, setIsEditingGeneral] = useState(false);
  const [isSavingGeneral, setIsSavingGeneral] = useState(false);
  const [isEditingCustomFields, setIsEditingCustomFields] = useState(false);
  const [isSavingCustomFields, setIsSavingCustomFields] = useState(false);
  const [editFormData, setEditFormData] = useState({
    name: '',
    email: '',
    phone: '',
    totalContacts: 0,
  });

  // State cho tags
  const [selectedTagIds, setSelectedTagIds] = useState<number[]>([]);
  const [selectedTagsData, setSelectedTagsData] = useState<Array<{ id: number; name: string; color?: string }>>([]);

  // Thêm/xóa trường tùy chỉnh vào audience - cấu trúc mới
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldData: { id: number; fieldKey?: string; label: string; dataType: string; description?: string; config?: Record<string, unknown> }) => {
      setAudienceCustomFields(prev => {
        const existingFieldIndex = prev.findIndex(field =>
          field.fieldKey === fieldData.fieldKey || field.id === fieldData.id
        );

        if (existingFieldIndex !== -1) {
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        // Thêm trường mới với cấu trúc MarketingCustomFieldResponse + value
        const newField: SelectedCustomField = {
          id: fieldData.id,
          fieldKey: fieldData.fieldKey || `field_${fieldData.id}`,
          displayName: fieldData.label,
          dataType: fieldData.dataType,
          ...(fieldData.description && { description: fieldData.description }),
          tags: [],
          config: fieldData.config || {},
          value: { value: '' }, // Giá trị mặc định
        };

        return [...prev, newField];
      });
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi audience
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setAudienceCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong audience
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string | number | boolean) => {
    setAudienceCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  // Xử lý thay đổi avatar
  const handleAvatarClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleAvatarChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      smartNotification.error({
        title: 'Lỗi định dạng file',
        message: t('marketing:audience.avatar.invalidFileType', 'Vui lòng chọn file hình ảnh'),
        duration: 3000,
      });
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      smartNotification.error({
        title: 'File quá lớn',
        message: t('marketing:audience.avatar.fileTooLarge', 'File quá lớn. Vui lòng chọn file nhỏ hơn 5MB'),
        duration: 3000,
      });
      return;
    }

    // Chỉ tạo preview, không upload ngay
    const previewUrl = URL.createObjectURL(file);
    setPendingAvatarFile(file);
    setPendingAvatarUrl(previewUrl);
    setAvatarUrl(previewUrl); // Hiển thị preview

    console.log('📸 Avatar file selected (pending upload):', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type
    });

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [t, smartNotification]);

  // Initialize form data when audience data is loaded
  useEffect(() => {
    if (audience) {
      setEditFormData({
        name: audience.name || '',
        email: audience.email || '',
        phone: audience.phone || '',
        totalContacts: audience.totalContacts || 0,
      });
    }
  }, [audience]);

  // Initialize custom fields from API response
  useEffect(() => {
    if (audience?.customFields && Array.isArray(audience.customFields)) {
      const transformedCustomFields: SelectedCustomField[] = audience.customFields.map((apiField: any) => ({
        id: parseInt(apiField.id) || 0,
        fieldKey: apiField.fieldName || '',
        displayName: apiField.fieldName || '', // Sử dụng fieldName làm displayName
        dataType: mapFieldTypeToDataType(apiField.fieldType), // Map từ API fieldType sang frontend dataType
        description: `Trường ${apiField.fieldName}`, // Tạo description mặc định
        tags: [],
        config: {}, // Có thể thêm config sau nếu cần
        value: { value: apiField.fieldValue || '' }, // Gán giá trị từ API
      }));

      console.log('🔄 Transformed custom fields from API:', transformedCustomFields);
      setAudienceCustomFields(transformedCustomFields);
    }
  }, [audience?.customFields]);

  // Initialize avatar URL from audience data
  useEffect(() => {
    if (audience) {
      // Ưu tiên sử dụng avatar từ API, fallback về placeholder
      const initialAvatarUrl = audience.avatar || `https://i.pravatar.cc/150?img=${audienceId}`;
      setAvatarUrl(initialAvatarUrl);

      console.log('🖼️ Initialize avatar URL:', {
        audienceAvatar: audience.avatar,
        fallbackUrl: `https://i.pravatar.cc/150?img=${audienceId}`,
        finalUrl: initialAvatarUrl
      });
    }
  }, [audience, audienceId]);

  // Initialize tags from API response
  useEffect(() => {
    if (audience?.tags && Array.isArray(audience.tags)) {
      const transformedTags = audience.tags.map((tag: any) => ({
        id: parseInt(tag.id) || 0,
        name: tag.name || '',
        color: tag.color || '#6B7280',
      }));

      const tagIds = transformedTags.map(tag => tag.id);

      console.log('🏷️ Initialize tags from API:', { transformedTags, tagIds });
      setSelectedTagsData(transformedTags);
      setSelectedTagIds(tagIds);
    }
  }, [audience?.tags]);

  // Xử lý bắt đầu chỉnh sửa thông tin chung
  const handleStartEditGeneral = useCallback(() => {
    setIsEditingGeneral(true);
  }, []);

  // Xử lý hủy chỉnh sửa thông tin chung
  const handleCancelEditGeneral = useCallback(() => {
    setIsEditingGeneral(false);

    // Reset form data to original values
    if (audience) {
      setEditFormData({
        name: audience.name || '',
        email: audience.email || '',
        phone: audience.phone || '',
        totalContacts: audience.totalContacts || 0,
      });
    }

    // Reset avatar về trạng thái ban đầu nếu có pending changes
    if (pendingAvatarFile) {
      const originalAvatarUrl = audience?.avatar || `https://i.pravatar.cc/150?img=${audienceId}`;
      setAvatarUrl(originalAvatarUrl);
      setPendingAvatarFile(null);

      if (pendingAvatarUrl) {
        URL.revokeObjectURL(pendingAvatarUrl);
        setPendingAvatarUrl('');
      }
    }

    // Reset tags về trạng thái ban đầu
    if (audience?.tags) {
      const originalTags = audience.tags.map((tag: any) => ({
        id: parseInt(tag.id) || 0,
        name: tag.name || '',
        color: tag.color || '#6B7280',
      }));
      const originalTagIds = originalTags.map(tag => tag.id);
      setSelectedTagsData(originalTags);
      setSelectedTagIds(originalTagIds);
    }
  }, [audience, pendingAvatarFile, pendingAvatarUrl, audienceId]);

  // Xử lý lưu thay đổi thông tin chung
  const handleSaveEditGeneral = useCallback(async () => {
    setIsSavingGeneral(true);
    setIsUploadingAvatar(true);

    try {
      // Chuẩn bị dữ liệu update theo format API expect
      const updateData: UpdateAudienceRequest = {
        name: editFormData.name,
        email: editFormData.email,
        phone: editFormData.phone,
        tagIds: selectedTagIds, // Gửi tagIds từ selectedTagIds
      };

      // Nếu có pending avatar file, thêm avatarMediaType
      if (pendingAvatarFile) {
        updateData.avatarMediaType = pendingAvatarFile.type; // Gửi media type thay vì avatar path
      }

      // Gọi API update audience - hook sẽ tự động hiển thị thông báo
      const response = await updateAudience.mutateAsync(updateData);

      // Nếu có pending avatar và API trả về upload URLs, thực hiện upload
      if (pendingAvatarFile && response.result?.avatarUploadUrl && response.result?.avatarS3Key) {
        try {
          // Upload file lên S3 sử dụng presigned URL
          const uploadResponse = await fetch(response.result.avatarUploadUrl, {
            method: 'PUT',
            body: pendingAvatarFile,
            headers: {
              'Content-Type': pendingAvatarFile.type,
            },
          });

          if (uploadResponse.ok) {
            // Cleanup pending avatar
            setPendingAvatarFile(null);
            if (pendingAvatarUrl) {
              URL.revokeObjectURL(pendingAvatarUrl);
              setPendingAvatarUrl('');
            }

            // Cập nhật avatar URL với URL cuối cùng từ API response
            // Sử dụng avatar field từ response thay vì presigned URL
            const finalAvatarUrl = response.result.avatar;
            if (finalAvatarUrl) {
              // Tạo full URL nếu cần
              const fullAvatarUrl = finalAvatarUrl.startsWith('http')
                ? finalAvatarUrl
                : `https://redaivn.hn.ss.bfcplatform.vn/${finalAvatarUrl}`;

              setAvatarUrl(fullAvatarUrl);
            }

            // Hiển thị thông báo thành công cho avatar upload
            smartNotification.success({
              title: 'Upload thành công',
              message: t('marketing:audience.avatar.uploadSuccess', 'Cập nhật avatar thành công'),
              duration: 3000,
            });

          } else {
            const errorText = await uploadResponse.text();
            console.error('❌ S3 upload failed:', {
              status: uploadResponse.status,
              statusText: uploadResponse.statusText,
              errorText
            });
            throw new Error(`Failed to upload avatar to S3: ${uploadResponse.status} - ${uploadResponse.statusText}`);
          }
        } catch (uploadError) {
          throw uploadError; // Re-throw để được xử lý ở catch block chính
        }
      } else if (pendingAvatarFile) {
        console.log('⚠️ No avatar upload URL received from API');
      }

      setIsEditingGeneral(false);
    } catch (error) {
      console.error('❌ Error saving audience:', error);

      // Reset avatar nếu có lỗi
      if (pendingAvatarFile) {
        const fallbackUrl = audience?.avatar || `https://i.pravatar.cc/150?img=${audienceId}`;
        setAvatarUrl(fallbackUrl);
        setPendingAvatarFile(null);
        if (pendingAvatarUrl) {
          URL.revokeObjectURL(pendingAvatarUrl);
          setPendingAvatarUrl('');
        }
      }

      // Hook đã xử lý hiển thị thông báo lỗi
    } finally {
      setIsSavingGeneral(false);
      setIsUploadingAvatar(false);
    }
  }, [editFormData, updateAudience, pendingAvatarFile, pendingAvatarUrl, audience, audienceId, t, smartNotification]);

  // Xử lý thay đổi form data
  const handleFormDataChange = useCallback((field: string, value: string | number) => {
    setEditFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  }, []);

  // Xử lý thay đổi phone number
  const handlePhoneChange = useCallback((value: string) => {
    setEditFormData(prev => ({
      ...prev,
      phone: value,
    }));
  }, []);

  // Xử lý thay đổi tags
  const handleTagsChange = useCallback((tagIds: number[]) => {
    setSelectedTagIds(tagIds);

    // Cập nhật selectedTagsData dựa trên tagIds mới
    // Giữ lại data của tags đã có, tạo placeholder cho tags mới
    const updatedTagsData = tagIds.map(tagId => {
      // Tìm tag data hiện có
      const existingTag = selectedTagsData.find(tag => tag.id === tagId);
      if (existingTag) return existingTag;

      // Tạo placeholder cho tag mới (sẽ được cập nhật khi component load data)
      return {
        id: tagId,
        name: `Tag ${tagId}`,
        color: '#6B7280'
      };
    });

    setSelectedTagsData(updatedTagsData);
  }, [selectedTagsData]);

  // Helper function để map dataType sang fieldType enum
  const mapDataTypeToFieldType = useCallback((dataType: string): 'TEXT' | 'NUMBER' | 'DATE' | 'BOOLEAN' => {
    switch (dataType?.toLowerCase()) {
      case 'number':
      case 'integer':
        return 'NUMBER';
      case 'date':
      case 'datetime':
        return 'DATE';
      case 'boolean':
      case 'checkbox':
        return 'BOOLEAN';
      case 'text':
      case 'string':
      case 'email':
      case 'phone':
      case 'textarea':
      default:
        return 'TEXT';
    }
  }, []);

  // Helper function để map fieldType từ API sang dataType frontend
  const mapFieldTypeToDataType = useCallback((fieldType: string): string => {
    switch (fieldType?.toUpperCase()) {
      case 'NUMBER':
        return 'number';
      case 'DATE':
        return 'date';
      case 'BOOLEAN':
        return 'boolean';
      case 'TEXT':
      default:
        return 'text';
    }
  }, []);

  // Xử lý bắt đầu chỉnh sửa custom fields
  const handleStartEditCustomFields = useCallback(() => {
    setIsEditingCustomFields(true);
  }, []);

  // Xử lý hủy chỉnh sửa custom fields
  const handleCancelEditCustomFields = useCallback(() => {
    setIsEditingCustomFields(false);
    // Reset custom fields về trạng thái ban đầu nếu cần
  }, []);

  // Xử lý lưu custom fields
  const handleSaveEditCustomFields = useCallback(async () => {
    setIsSavingCustomFields(true);
    try {
      // Transform audienceCustomFields to BulkUpdateCustomFieldsDto format
      const bulkUpdateData: BulkUpdateCustomFieldsDto = {
        fields: audienceCustomFields.map((field): CreateCustomFieldDto => ({
          fieldName: field.fieldKey || field.displayName || '', // Sử dụng fieldKey hoặc displayName
          fieldType: mapDataTypeToFieldType(field.dataType), // Map dataType sang fieldType enum
          fieldValue: field.value?.['value'] ?? '',
        })),
      };

      console.log('🚀 Bulk update custom fields data:', bulkUpdateData);

      // Call API to bulk update custom fields
      await bulkUpdateCustomFields.mutateAsync(bulkUpdateData);

      setIsEditingCustomFields(false);

      // Hiển thị thông báo thành công
      smartNotification.success({
        title: 'Lưu thành công',
        message: t('marketing:audience.customFields.saveSuccess', 'Lưu trường tùy chỉnh thành công'),
        duration: 3000,
      });

    } catch (error) {
      console.error('Error saving custom fields:', error);

      // Hiển thị thông báo lỗi
      smartNotification.error({
        title: 'Lỗi lưu dữ liệu',
        message: t('marketing:audience.customFields.saveError', 'Có lỗi xảy ra khi lưu trường tùy chỉnh'),
        duration: 3000,
      });
    } finally {
      setIsSavingCustomFields(false);
    }
  }, [audienceCustomFields, bulkUpdateCustomFields, t, smartNotification]);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loading />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Icon name="alert-circle" className="w-16 h-16 text-red-300 mb-4 mx-auto" />
          <Typography variant="h6" className="text-red-600 mb-2">
            {t('marketing:audience.loadError')}
          </Typography>
          <Typography variant="body2" className="text-gray-500">
            {error.message}
          </Typography>
        </div>
      </div>
    );
  }

  // Not found state
  if (!audience) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Icon name="users" className="w-16 h-16 text-gray-300 mb-4 mx-auto" />
          <Typography variant="h6" className="text-gray-500 mb-2">
            {t('marketing:audience.notFound')}
          </Typography>
          <Typography variant="body2" className="text-gray-400">
            {t('marketing:audience.notFoundDescription')}
          </Typography>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-border pb-4">
        <div>
          <Typography variant="h4" className="text-foreground">
            {t('marketing:audience.detailForm')}
          </Typography>
          <Typography variant="body2" className="text-muted mt-1">
            {audience.name}
          </Typography>
        </div>
        <div className="flex space-x-3">
          {onEdit && (
            <IconCard
              icon="settings"
              variant="secondary"
              size="md"
              title={t('marketing:audience.edit.advanced', 'Cài đặt nâng cao')}
              onClick={onEdit}
            />
          )}
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('marketing:audience.close', 'Đóng')}
            onClick={onClose}
          />
        </div>
      </div>

      {/* Thông tin chung */}
      <CollapsibleCard
        title={
          <div className="flex items-center justify-between w-full">
            <Typography variant="h6" className="font-medium">
              {t('marketing:audience.generalInfo', 'Thông tin chung')}
            </Typography>
            <div className="flex space-x-2" onClick={(e) => e.stopPropagation()}>
              {isEditingGeneral ? (
                <>
                  <IconCard
                    icon="x"
                    variant="ghost"
                    size="sm"
                    title={t('marketing:audience.edit.cancel', 'Hủy')}
                    onClick={handleCancelEditGeneral}
                  />
                  <IconCard
                    icon="check"
                    variant="primary"
                    size="sm"
                    title={t('marketing:audience.edit.save', 'Lưu')}
                    onClick={handleSaveEditGeneral}
                    disabled={isSavingGeneral}
                  />
                </>
              ) : (
                <IconCard
                  icon="edit"
                  variant="ghost"
                  size="sm"
                  title={t('marketing:audience.edit.title', 'Chỉnh sửa')}
                  onClick={handleStartEditGeneral}
                />
              )}
            </div>
          </div>
        }
        defaultOpen={true}
        className="mb-4"
      >

        {/* Avatar và thông tin cơ bản */}
        <div className="flex items-start space-x-4 mb-6">
          <div className="relative flex flex-col items-center">
            <div
              className="relative cursor-pointer group"
              onClick={handleAvatarClick}
              title={t('marketing:audience.avatar.clickToChange', 'Nhấp để thay đổi avatar')}
            >
              <Avatar
                src={avatarUrl}
                alt={audience.name}
                size="3xl"
                className={`transition-all duration-200 ${isUploadingAvatar ? 'opacity-50' : 'group-hover:opacity-80'}`}
              />
              {/* Overlay icon */}
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-black bg-opacity-30 rounded-full">
                <Icon name="camera" className="w-8 h-8 text-white" />
              </div>
              {/* Loading indicator */}
              {isUploadingAvatar && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <Loading size="sm" />
                </div>
              )}
            </div>
            <Typography variant="caption" className="text-muted mt-2 text-center">
              {t('marketing:audience.avatar.clickToChange', 'Nhấp để thay đổi avatar')}
            </Typography>
            {/* Hidden file input */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleAvatarChange}
              className="hidden"
            />
          </div>

          <div className="flex-1">
            <Typography variant="body2" className="text-muted mb-2">
              ID: {audience.id}
            </Typography>
            <Typography variant="h5" className="text-foreground font-semibold mb-1">
              {audience.name}
            </Typography>
            {audience.email && (
              <Typography variant="body2" className="text-muted">
                {audience.email}
              </Typography>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* ID Field - Always read-only */}
          <div>
            <Typography variant="body2" className="text-muted mb-1">
              {t('marketing:audience.id', 'ID')}
            </Typography>
            <Typography variant="body1" className="text-foreground font-medium">
              {audience.id}
            </Typography>
          </div>

          {/* Name Field */}
          <div>
            <Typography variant="body2" className="text-muted mb-1">
              {t('marketing:audience.name', 'Tên đối tượng')}
            </Typography>
            {isEditingGeneral ? (
              <Input
                value={editFormData.name}
                onChange={(e) => handleFormDataChange('name', e.target.value)}
                placeholder={t('marketing:audience.name.placeholder', 'Nhập tên đối tượng')}
                fullWidth
              />
            ) : (
              <Typography variant="body1" className="text-foreground font-medium">
                {audience.name}
              </Typography>
            )}
          </div>

          {/* Email Field */}
          <div>
            <Typography variant="body2" className="text-muted mb-1">
              {t('marketing:audience.email', 'Email')}
            </Typography>
            {isEditingGeneral ? (
              <Input
                type="email"
                value={editFormData.email}
                onChange={(e) => handleFormDataChange('email', e.target.value)}
                placeholder={t('marketing:audience.email.placeholder', 'Nhập email')}
                fullWidth
              />
            ) : (
              <Typography variant="body1" className="text-foreground font-medium">
                {audience.email || t('marketing:audience.noData', 'Chưa có dữ liệu')}
              </Typography>
            )}
          </div>

          {/* Phone Field */}
          <div>
            <Typography variant="body2" className="text-muted mb-1">
              {t('marketing:audience.phone', 'Số điện thoại')}
            </Typography>
            {isEditingGeneral ? (
              <PhoneInputWithCountry
                value={editFormData.phone}
                onChange={handlePhoneChange}
                placeholder="Nhập số điện thoại"
                fullWidth
              />
            ) : (
              <Typography variant="body1" className="text-foreground font-medium">
                {audience.phone || t('marketing:audience.noData', 'Chưa có dữ liệu')}
              </Typography>
            )}
          </div>

          {/* Total Contacts Field - Always read-only */}
          <div>
            <Typography variant="body2" className="text-muted mb-1">
              {t('marketing:audience.totalContacts', 'Tổng số liên hệ')}
            </Typography>
            <Typography variant="body1" className="text-foreground font-medium">
              {audience.totalContacts || 0}
            </Typography>
          </div>

          {/* Tags Field */}
          <div className="md:col-span-2">
            <Typography variant="body2" className="text-muted mb-1">
              {t('marketing:audience.tags', 'Tags')}
            </Typography>
            {isEditingGeneral ? (
              <AdminTagsSelectWithPagination
                value={selectedTagIds}
                onChange={handleTagsChange}
                placeholder="Chọn tags..."
                fullWidth
              />
            ) : (
              <div className="flex flex-wrap gap-2">
                {selectedTagsData.length > 0 ? (
                  selectedTagsData.map((tag) => (
                    <span
                      key={tag.id}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white"
                      style={{ backgroundColor: tag.color || '#6B7280' }}
                    >
                      {tag.name}
                    </span>
                  ))
                ) : (
                  <Typography variant="body1" className="text-muted">
                    {t('marketing:audience.noTags', 'Chưa có tags')}
                  </Typography>
                )}
              </div>
            )}
          </div>
        </div>
      </CollapsibleCard>

      {/* Trường tùy chỉnh */}
      <CollapsibleCard
        title={
          <div className="flex items-center justify-between w-full">
            <Typography variant="h6" className="font-medium">
              {t('marketing:audience.customFields.title', 'Trường tùy chỉnh')}
            </Typography>
            <div className="flex space-x-2" onClick={(e) => e.stopPropagation()}>
              {isEditingCustomFields ? (
                <>
                  <IconCard
                    icon="x"
                    variant="ghost"
                    size="sm"
                    title={t('marketing:audience.edit.cancel', 'Hủy')}
                    onClick={handleCancelEditCustomFields}
                  />
                  <IconCard
                    icon="check"
                    variant="primary"
                    size="sm"
                    title={t('marketing:audience.customFields.save', 'Lưu trường tùy chỉnh')}
                    onClick={handleSaveEditCustomFields}
                    disabled={isSavingCustomFields}
                  />
                </>
              ) : (
                <IconCard
                  icon="edit"
                  variant="ghost"
                  size="sm"
                  title={t('marketing:audience.customFields.edit', 'Chỉnh sửa trường tùy chỉnh')}
                  onClick={handleStartEditCustomFields}
                />
              )}
            </div>
          </div>
        }
        defaultOpen={false}
        className="mb-4"
      >
        <div className="space-y-4">
          <GenericCustomFieldSelector
            onFieldSelect={(fieldData) => {
              handleToggleCustomFieldToProduct(fieldData);
            }}
            selectedFieldIds={audienceCustomFields.map(f => f.id || 0).filter(id => id > 0)}
            placeholder={t(
              'marketing:audience.customFields.searchPlaceholder',
              'Nhấp để tải dữ liệu hoặc nhập từ khóa để tìm kiếm...'
            )}
            searchFunction={searchFunction}
            title={t('marketing:customField.title', 'Trường tùy chỉnh')}
            translationNamespace="marketing"
            usePortal={true}
          />

          {audienceCustomFields.length > 0 && (
            <div className="space-y-3">
              {audienceCustomFields.map(field => {
                const fieldId = field.id || 0;
                // Adapter để chuyển đổi cấu trúc mới sang cấu trúc cũ cho CustomFieldRenderer
                const adaptedField = {
                  id: fieldId,
                  fieldId: fieldId,
                  label: field.displayName,
                  component: field.dataType as string,
                  type: field.dataType as string,
                  required: false,
                  configJson: field.config as Record<string, unknown>,
                  value: field.value,
                };

                return (
                  <CustomFieldRenderer
                    key={fieldId}
                    field={adaptedField}
                    value={String(field.value?.['value'] ?? '')}
                    onChange={value => handleUpdateCustomFieldInProduct(fieldId, value)}
                    onRemove={() => handleRemoveCustomFieldFromProduct(fieldId)}
                  />
                );
              })}
            </div>
          )}

          {/* Display existing attributes from audience data */}
          {audience.attributes && audience.attributes.length > 0 && (
            <div className="space-y-3">
              <Typography variant="body2" className="font-medium text-muted">
                {t('marketing:audience.existingAttributes', 'Thuộc tính hiện có:')}
              </Typography>
              {audience.attributes.map((attribute) => (
                <div key={attribute.id} className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
                  <Typography variant="body2" className="text-muted">
                    {attribute.name}
                  </Typography>
                  <Typography variant="body1" className="text-foreground font-medium">
                    {attribute.value}
                  </Typography>
                </div>
              ))}
            </div>
          )}
        </div>
      </CollapsibleCard>
    </div>
  );
};

export default AudienceDetailView;
