import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useLocation } from 'react-router-dom';

interface SEOWrapperProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  keywords?: string;
  noIndex?: boolean;
}

/**
 * SEO Wrapper component để quản lý meta tags
 * Tự động thêm noindex, nofollow cho tất cả trang trừ trang login
 */
const SEOWrapper: React.FC<SEOWrapperProps> = ({
  children,
  title,
  description,
  keywords,
  noIndex
}) => {
  const location = useLocation();
  
  // Danh sách các trang login không cần noindex
  const loginPaths = ['/auth', '/admin/auth'];
  
  // Kiểm tra xem có phải trang login không
  const isLoginPage = loginPaths.some(path => 
    location.pathname === path || location.pathname.startsWith(path + '/')
  );
  
  // Quyết định có thêm noindex hay không
  const shouldNoIndex = noIndex !== undefined ? noIndex : !isLoginPage;

  return (
    <>
      <Helmet>
        {title && <title>{title}</title>}
        {description && <meta name="description" content={description} />}
        {keywords && <meta name="keywords" content={keywords} />}
        {shouldNoIndex && <meta name="robots" content="noindex, nofollow" />}
      </Helmet>
      {children}
    </>
  );
};

export default SEOWrapper;
