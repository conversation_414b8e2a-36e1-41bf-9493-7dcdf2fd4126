import React, { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, ActionMenuItem, StatusBadge } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import {
  AudienceStatus,
  AudienceQueryParams,
  AudienceAttribute,
  ContactData,
  CreateAudienceRequest,
} from '../types/audience.types';
import AudienceForm from '../components/forms/AudienceForm';
import AudienceDetailView from '../components/forms/AudienceDetailView';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useAudiences, useCreateAudience, useDeleteMultipleAudiences } from '../hooks';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { NotificationUtil } from '@/shared/utils/notification';
import ConfirmDeleteModal from '@/shared/components/common/ConfirmDeleteModal';

/**
 * Interface cho dữ liệu audience từ API (thực tế là ContactData)
 */
interface AudienceData {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string | undefined;
  customFields: any[];
  tags: any[];
  createdAt: string;
  updatedAt: string;
}



/**
 * Trang quản lý đối tượng sử dụng các hooks tối ưu
 */
const AudiencePage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho bulk delete
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // State cho detail view
  const [selectedAudienceId, setSelectedAudienceId] = useState<number | null>(null);
  const [showDetailView, setShowDetailView] = useState(false);

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      { id: 'active', label: t('common:active'), icon: 'check', value: AudienceStatus.ACTIVE },
      { id: 'draft', label: t('common:draft'), icon: 'file', value: AudienceStatus.DRAFT },
      {
        id: 'inactive',
        label: t('common:inactive'),
        icon: 'eye-off',
        value: AudienceStatus.INACTIVE,
      },
    ],
    [t]
  );

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<AudienceData>[]>(
    () => [
      { key: 'id', title: t('common:id', 'ID'), dataIndex: 'id', width: '8%', sortable: true },
      {
        key: 'avatar',
        title: t('admin:marketing.audience.avatar', 'Avatar'),
        dataIndex: 'avatar',
        width: '10%',
        render: (value: unknown, record: AudienceData) => {
          const avatarUrl = value as string;
          return (
            <div className="flex items-center justify-center">
              {avatarUrl ? (
                <img
                  src={avatarUrl}
                  alt={record.name}
                  className="w-8 h-8 rounded-full object-cover border border-gray-200"
                  onError={(e) => {
                    // Fallback to default avatar if image fails to load
                    const target = e.target as HTMLImageElement;
                    target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(record.name)}&background=random`;
                  }}
                />
              ) : (
                <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 text-xs font-medium">
                  {record.name?.charAt(0)?.toUpperCase() || '?'}
                </div>
              )}
            </div>
          );
        },
      },
      {
        key: 'name',
        title: t('admin:marketing.audience.name', 'Tên'),
        dataIndex: 'name',
        width: '20%',
        sortable: true,
        render: (value: unknown) => (
          <div className="font-medium text-gray-900 dark:text-gray-100">
            {value as string}
          </div>
        ),
      },
      {
        key: 'email',
        title: t('admin:marketing.audience.email', 'Email'),
        dataIndex: 'email',
        width: '22%',
        sortable: true,
        render: (value: unknown) => (
          <div className="text-gray-600 dark:text-gray-400">
            {value as string}
          </div>
        ),
      },
      {
        key: 'phone',
        title: t('admin:marketing.audience.phone', 'Số điện thoại'),
        dataIndex: 'phone',
        width: '15%',
        sortable: true,
        render: (value: unknown) => (
          <div className="text-gray-600 dark:text-gray-400">
            {value as string}
          </div>
        ),
      },
      {
        key: 'tags',
        title: t('admin:marketing.audience.tags', 'Tags'),
        dataIndex: 'tags',
        width: '18%',
        render: (value: unknown, _record: AudienceData) => {
          const tags = value as Array<{ id: string; name: string; color?: string }>;

          if (!tags || tags.length === 0) {
            return (
              <div className="text-gray-400 text-sm italic">
                {t('admin:marketing.audience.noTags', 'Chưa có tags')}
              </div>
            );
          }

          // Hiển thị tối đa 2 tags, nếu có nhiều hơn thì hiển thị "+X more"
          const maxDisplayTags = 2;
          const displayTags = tags.slice(0, maxDisplayTags);
          const remainingCount = tags.length - maxDisplayTags;

          return (
            <div className="flex flex-wrap gap-1">
              {displayTags.map((tag) => (
                <StatusBadge
                  key={tag.id}
                  text={tag.name}
                  variant="info"
                  className="text-xs"
                />
              ))}
              {remainingCount > 0 && (
                <StatusBadge
                  text={`+${remainingCount}`}
                  variant="primary"
                  className="text-xs"
                />
              )}
            </div>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('common:createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          // Chuyển đổi timestamp thành ngày
          const date = new Date(Number(value) * 1000);
          return (
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {date.toLocaleDateString('vi-VN')}
            </div>
          );
        },
      },
      {
        key: 'actions',
        title: t('common:actions', 'Thao tác'),
        width: '9%',
        render: (_: unknown, record: AudienceData) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('common:view', 'Xem'),
              icon: 'eye',
              onClick: () => handleViewDetail(parseInt(record.id, 10)),
            }
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): AudienceQueryParams => {
    console.log('createQueryParams called with:', params); // Debug log
    const queryParams: AudienceQueryParams = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all') {
      queryParams.status = params.filterValue as AudienceStatus;
    }

    console.log('Generated query params:', queryParams); // Debug log
    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<AudienceData, AudienceQueryParams>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Sử dụng hooks từ API với query params từ dataTable
  const { data: apiAudienceData, isLoading } = useAudiences(dataTable.queryParams);

  const audienceData = useMemo(() => {

    // Lấy dữ liệu từ API response
    const rawData = apiAudienceData?.items || [];

    if (!rawData || rawData.length === 0) {
      return { items: [], meta: { currentPage: 1, totalItems: 0 } };
    }

    // Chuyển đổi mỗi item từ API response sang AudienceData
    // API trả về dữ liệu contact với avatar, name, email, phone
    const items = rawData.map((contact: ContactData, index: number): AudienceData => ({
      id: contact.id?.toString() || index.toString(),
      name: contact.name || `Contact ${contact.id || index + 1}`,
      email: contact.email || '',
      phone: contact.phone || '',
      avatar: contact.avatar || undefined,
      customFields: contact.customFields || [],
      tags: contact.tags || [],
      createdAt: contact.createdAt,
      updatedAt: contact.updatedAt,
    }));

    return {
      items,
      meta: apiAudienceData?.meta || { currentPage: 1, totalItems: items.length },
    };
  }, [apiAudienceData]);
  const createAudienceMutation = useCreateAudience();
  const { mutateAsync: deleteMultipleAudiences } = useDeleteMultipleAudiences();

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();



  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      NotificationUtil.warning({
        message: t('admin:marketing.audience.selectToDelete', 'Vui lòng chọn audience để xóa'),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      // Chuyển đổi selectedRowKeys thành number[]
      const ids = selectedRowKeys.map(key => {
        if (typeof key === 'string') {
          return parseInt(key, 10);
        }
        return key as number;
      }).filter(id => !isNaN(id));

      // Gọi API xóa nhiều audiences cùng lúc
      await deleteMultipleAudiences(ids);

      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('admin:marketing.audience.bulkDeleteSuccess', 'Xóa {{count}} audience thành công', { count: selectedRowKeys.length }),
        duration: 3000,
      });
    } catch {
      NotificationUtil.error({
        message: t('admin:marketing.audience.bulkDeleteError', 'Xóa nhiều audience thất bại'),
        duration: 3000,
      });
    }
  }, [selectedRowKeys, deleteMultipleAudiences, t]);

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [AudienceStatus.ACTIVE]: t('common:active'),
      [AudienceStatus.DRAFT]: t('common:draft'),
      [AudienceStatus.INACTIVE]: t('common:inactive'),
    },
    t,
  });

  // Xử lý thêm mới
  const handleAdd = () => {
    showAddForm();
  };

  // Xử lý xem chi tiết
  const handleViewDetail = (audienceId: number) => {
    setSelectedAudienceId(audienceId);
    setShowDetailView(true);
  };

  // Xử lý đóng detail view
  const handleCloseDetailView = () => {
    setShowDetailView(false);
    setSelectedAudienceId(null);
  };

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    // Chuyển đổi values thành CreateAudienceRequest (không gửi countryCode vì API không chấp nhận)
    const audienceData: CreateAudienceRequest = {
      name: values['name'] as string,
      ...(values['email'] ? { email: values['email'] as string } : {}),
      ...(values['phone'] ? { phone: values['phone'] as string } : {}),
    };

    if (values['tagIds']) {
      audienceData.tagIds = values['tagIds'] as number[];
    }

    if (values['attributes']) {
      audienceData.attributes = values['attributes'] as Omit<AudienceAttribute, 'id'>[];
    }

    createAudienceMutation.mutate(audienceData, {
      onSuccess: () => {
        NotificationUtil.success({
          message: t('admin:marketing.audience.createSuccess', 'Tạo đối tượng thành công'),
          duration: 3000,
        });
        hideAddForm();
      },
      onError: (error) => {
        console.error('Error creating audience:', error); // Debug log
        NotificationUtil.error({
          message: t('admin:marketing.audience.createError', 'Tạo đối tượng thất bại'),
          duration: 3000,
        });
      },
    });
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideAddForm();
  };

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete', 'Xóa nhiều'),
            variant: 'primary',
            onClick: handleShowBulkDeleteConfirm,
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form thêm mới */}
      <SlideInForm isVisible={isAddFormVisible}>
        <AudienceForm onSubmit={handleSubmit} onCancel={handleCancel} />
      </SlideInForm>

      {/* Detail View */}
      <SlideInForm isVisible={showDetailView}>
        {selectedAudienceId && (
          <AudienceDetailView
            audienceId={selectedAudienceId}
            onClose={handleCloseDetailView}
          />
        )}
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={audienceData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: audienceData?.meta?.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: audienceData?.meta?.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [5, 10, 15, 20],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={t('admin:marketing.audience.confirmBulkDeleteMessage', 'Bạn có chắc chắn muốn xóa {{count}} audience đã chọn?', { count: selectedRowKeys.length })}
      />
    </div>
  );
};

export default AudiencePage;
