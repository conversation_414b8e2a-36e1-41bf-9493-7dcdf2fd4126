import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  IconCard,
  Select,
  Typography,
  Loading,
} from '@/shared/components/common';
import { z } from 'zod';

// Import enum từ types
import { CustomFieldType } from '../../types/custom-field.types';
import { useCustomField } from '../../hooks';

// Schema cho form chỉnh sửa
const getFormSchema = (t: (key: string) => string) =>
  z.object({
    // fieldKey chỉ để hiển thị, không gửi trong update request
    fieldKey: z.string().optional(),
    displayName: z
      .string()
      .min(1, t('marketing:customFields.form.validation.displayNameRequired')),
    dataType: z.nativeEnum(CustomFieldType, {
      errorMap: () => ({
        message: t('marketing:customFields.form.validation.dataTypeRequired'),
      }),
    }),
    description: z.string().optional(),
  });

export type EditCustomFieldFormValues = z.infer<ReturnType<typeof getFormSchema>>;

interface EditCustomFieldFormProps {
  id: string;
  onSubmit: (id: string, values: Record<string, unknown>) => void;
  onCancel: () => void;
}

/**
 * Component form chỉnh sửa trường tùy chỉnh
 */
const EditCustomFieldForm: React.FC<EditCustomFieldFormProps> = ({ id, onSubmit, onCancel }) => {
  const { t } = useTranslation(['marketing', 'common']);
  const { data: customField, isLoading } = useCustomField(id);

  const formSchema = getFormSchema(t);

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    // Loại bỏ fieldKey khỏi data gửi đi vì API không cho phép cập nhật fieldKey
    const { fieldKey, ...updateData } = values;
    onSubmit(id, updateData);
  };

  // Chuẩn bị giá trị mặc định cho form
  const defaultValues = customField
    ? {
        fieldKey: customField.fieldKey,
        displayName: customField.displayName,
        dataType: customField.dataType,
        description: customField.description || '',
      }
    : undefined;

  if (isLoading) {
    return (
      <Card className="mb-4 p-4 min-h-[300px] flex items-center justify-center">
        <Loading className="rounded-lg" />
      </Card>
    );
  }

  if (!customField) {
    return (
      <Card className="mb-4 p-4">
        <Typography variant="body1" className="text-red-500">
          {t('marketing:customFields.notFound')}
        </Typography>
        <div className="flex justify-end mt-4">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('common:cancel')}
            onClick={onCancel}
          />
        </div>
      </Card>
    );
  }

  return (
    <Card className="mb-4 p-4">
      <Typography variant="h5" className="mb-4">
        {t('marketing:customFields.edit')}
      </Typography>

      <Form schema={formSchema} onSubmit={handleSubmit} defaultValues={defaultValues} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="fieldKey" label={t('marketing:customFields.form.fieldKeyLabel')}>
            <Input
              value={customField.fieldKey}
              fullWidth
              disabled
              helperText={t('marketing:customFields.form.fieldKeyReadOnly')}
            />
          </FormItem>

          <FormItem name="displayName" label={t('marketing:customFields.form.displayNameLabel')} required>
            <Input
              placeholder={t('marketing:customFields.form.displayNamePlaceholder')}
              fullWidth
            />
          </FormItem>
        </div>

        <FormItem name="dataType" label={t('marketing:customFields.form.dataTypeLabel')} required>
          <Select
            options={[
              { value: CustomFieldType.TEXT, label: t('marketing:customFields.types.text') },
              { value: CustomFieldType.NUMBER, label: t('marketing:customFields.types.number') },
              { value: CustomFieldType.DATE, label: t('marketing:customFields.types.date') },
              { value: CustomFieldType.BOOLEAN, label: t('marketing:customFields.types.boolean') },
            ]}
            placeholder={t('marketing:customFields.form.dataTypePlaceholder')}
            fullWidth
          />
        </FormItem>

        <FormItem name="description" label={t('marketing:customFields.form.descriptionLabel')}>
          <Input
            placeholder={t('marketing:customFields.form.descriptionPlaceholder')}
            fullWidth
          />
        </FormItem>

        <div className="flex justify-end space-x-2 pt-4">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('common:cancel')}
            onClick={onCancel}
          />
          <IconCard
            icon="save"
            variant="primary"
            size="md"
            title={t('common:save')}
            onClick={() => {
              // Trigger form submit programmatically
              const form = document.querySelector('form');
              if (form) {
                form.requestSubmit();
              }
            }}
          />
        </div>
      </Form>
    </Card>
  );
};

export default EditCustomFieldForm;
