/**
 * Hooks for segment API using TanStack Query
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { SegmentService } from '../services/segment.service';
import {
  CreateSegmentRequest,
  SegmentListResponse,
  SegmentQueryParams,
  UpdateSegmentRequest,
} from '../types/segment.types';

/**
 * Query keys for segment API
 */
export const SEGMENT_QUERY_KEYS = {
  all: ['marketing', 'segments'] as const,
  list: (params: SegmentQueryParams) => [...SEGMENT_QUERY_KEYS.all, 'list', params] as const,
  detail: (id: string | number) => [...SEGMENT_QUERY_KEYS.all, 'detail', id] as const,
  stats: (id: string | number) => [...SEGMENT_QUERY_KEYS.all, 'stats', id] as const,
};

/**
 * Hook to get segments with pagination and filtering
 */
export const useSegments = (params: SegmentQueryParams = {}): ReturnType<typeof useQuery<SegmentListResponse>> => {
  return useQuery({
    queryKey: SEGMENT_QUERY_KEYS.list(params),
    queryFn: () => SegmentService.getSegments(params),
    select: (data): SegmentListResponse => {
      // API trả về { code, message, result: { data: [...], meta: {...} } }
      // Transform để khớp với component expect { items, meta }
      const result = data.result as unknown as { data: any[]; meta: Record<string, unknown> };
      if (result && 'data' in result) {
        return {
          items: result.data,
          meta: {
            currentPage: result.meta?.['page'] as number || 1,
            itemsPerPage: result.meta?.['limit'] as number || 10,
            totalItems: result.meta?.['total'] as number || 0,
            totalPages: result.meta?.['totalPages'] as number || 1,
            itemCount: result.data?.length || 0,
            hasItems: (result.data?.length || 0) > 0,
            hasPreviousPage: result.meta?.['hasPreviousPage'] as boolean || false,
            hasNextPage: result.meta?.['hasNextPage'] as boolean || false,
          }
        };
      }
      // Fallback - should not happen with proper API
      return {
        items: [],
        meta: {
          currentPage: 1,
          itemsPerPage: 10,
          totalItems: 0,
          totalPages: 1,
          itemCount: 0,
          hasItems: false,
          hasPreviousPage: false,
          hasNextPage: false,
        }
      };
    },
  });
};

/**
 * Hook to get segment by ID
 */
export const useSegment = (id: string | number) => {
  return useQuery({
    queryKey: SEGMENT_QUERY_KEYS.detail(id),
    queryFn: () => SegmentService.getSegmentById(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook to get segment statistics
 */
export const useSegmentStats = (id: string | number) => {
  return useQuery({
    queryKey: SEGMENT_QUERY_KEYS.stats(id),
    queryFn: () => SegmentService.getSegmentStats(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook to create segment
 */
export const useCreateSegment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateSegmentRequest) => SegmentService.createSegment(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: SEGMENT_QUERY_KEYS.all });
    },
  });
};

/**
 * Hook to update segment
 */
export const useUpdateSegment = (id: string | number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateSegmentRequest) => SegmentService.updateSegment(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: SEGMENT_QUERY_KEYS.detail(id) });
      queryClient.invalidateQueries({ queryKey: SEGMENT_QUERY_KEYS.stats(id) });
      queryClient.invalidateQueries({ queryKey: SEGMENT_QUERY_KEYS.all });
    },
  });
};

/**
 * Hook to delete segment
 */
export const useDeleteSegment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string | number) => SegmentService.deleteSegment(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: SEGMENT_QUERY_KEYS.all });
    },
  });
};

/**
 * Hook to delete multiple segments - API mới với response chi tiết
 */
export const useBulkDeleteSegments = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: (string | number)[]) => SegmentService.deleteMultipleSegments(ids),
    onSuccess: () => {
      // Invalidate queries để refresh data
      queryClient.invalidateQueries({ queryKey: SEGMENT_QUERY_KEYS.all });
    },
    onError: (error) => {
      console.error('❌ Bulk delete failed:', error);
    },
  });
};
